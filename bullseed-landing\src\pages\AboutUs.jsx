import React from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

const AboutUs = () => {
  const stats = [
    {
      number: '36M+',
      label: 'Registered Users',
      icon: '👥'
    },
    {
      number: '178',
      label: 'Countries Supported',
      icon: '🌍'
    },
    {
      number: '$10M+',
      label: 'Monthly Withdrawals',
      icon: '💰'
    },
    {
      number: '18k+',
      label: 'Daily Active Investors',
      icon: '📈'
    }
  ];

  const teamMembers = [
    {
      name: '<PERSON>',
      position: 'Chief Executive Officer',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
      bio: 'Former Goldman Sachs executive with 15+ years in algorithmic trading and fintech innovation.'
    },
    {
      name: '<PERSON>',
      position: 'Chief Technology Officer',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
      bio: 'Ex-Google engineer specializing in blockchain technology and high-frequency trading systems.'
    },
    {
      name: '<PERSON>',
      position: 'Head of Quantitative Research',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
      bio: 'PhD in Mathematical Finance from MIT, former hedge fund manager with expertise in crypto markets.'
    },
    {
      name: 'Emily Zhang',
      position: 'Chief Risk Officer',
      image: 'https://images.unsplash.com/photo-*************-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
      bio: 'Former JP Morgan risk analyst with deep expertise in cryptocurrency market dynamics and compliance.'
    }
  ];

  const values = [
    {
      icon: '🔒',
      title: 'Security First',
      description: 'Bank-grade security protocols and multi-layer encryption protect your investments 24/7.'
    },
    {
      icon: '📊',
      title: 'Data-Driven',
      description: 'Advanced AI algorithms analyze market patterns to optimize investment strategies in real-time.'
    },
    {
      icon: '🌐',
      title: 'Global Reach',
      description: 'Serving investors across 178 countries with localized support and compliance.'
    },
    {
      icon: '⚡',
      title: 'Innovation',
      description: 'Cutting-edge technology meets traditional finance to create the future of investing.'
    }
  ];

  return (
    <div className="about-page">
      <Navbar />
      
      {/* Hero Section */}
      <section className="about-hero">
        <div className="about-hero-container">
          <div className="about-hero-content">
            <div className="about-breadcrumb">
              <a href="/">Home</a>
              <span>/</span>
              <span>About Us</span>
            </div>
            <h1 className="about-hero-title">
              Revolutionizing <span className="highlight">Crypto Investment</span>
            </h1>
            <p className="about-hero-description">
              BullSeed is the world's most advanced cryptocurrency investment platform, 
              combining institutional-grade algorithms with user-friendly design to democratize 
              access to professional trading strategies.
            </p>
          </div>
          <div className="about-hero-image">
            <div className="about-hero-visual">
              <div className="floating-card card-1">
                <div className="card-icon">📈</div>
                <div className="card-text">AI Trading</div>
              </div>
              <div className="floating-card card-2">
                <div className="card-icon">🔒</div>
                <div className="card-text">Secure</div>
              </div>
              <div className="floating-card card-3">
                <div className="card-icon">⚡</div>
                <div className="card-text">Fast</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="about-stats">
        <div className="about-stats-container">
          <div className="about-stats-grid">
            {stats.map((stat, index) => (
              <div key={index} className="about-stat-card">
                <div className="about-stat-icon">{stat.icon}</div>
                <div className="about-stat-number">{stat.number}</div>
                <div className="about-stat-label">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="about-mission">
        <div className="about-mission-container">
          <div className="about-mission-content">
            <div className="about-mission-text">
              <h2 className="about-mission-title">
                Our <span className="highlight">Mission</span>
              </h2>
              <p className="about-mission-description">
                At BullSeed, we believe that sophisticated investment strategies shouldn't be 
                exclusive to Wall Street. Our mission is to democratize access to institutional-grade 
                cryptocurrency trading through advanced AI algorithms and user-centric design.
              </p>
              <p className="about-mission-description">
                We've assembled a world-class team of quantitative analysts, blockchain engineers, 
                and financial experts who work tirelessly to ensure our platform delivers consistent, 
                transparent, and profitable results for investors of all levels.
              </p>
            </div>
            <div className="about-mission-visual">
              <div className="mission-graphic">
                <div className="mission-circle circle-1"></div>
                <div className="mission-circle circle-2"></div>
                <div className="mission-circle circle-3"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="about-values">
        <div className="about-values-container">
          <h2 className="about-values-title">
            Our <span className="highlight">Core Values</span>
          </h2>
          <div className="about-values-grid">
            {values.map((value, index) => (
              <div key={index} className="about-value-card">
                <div className="about-value-icon">{value.icon}</div>
                <h3 className="about-value-title">{value.title}</h3>
                <p className="about-value-description">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="about-team">
        <div className="about-team-container">
          <div className="about-team-header">
            <h2 className="about-team-title">
              Meet Our <span className="highlight">Expert Team</span>
            </h2>
            <p className="about-team-subtitle">
              Industry veterans and innovators driving the future of cryptocurrency investment
            </p>
          </div>
          <div className="about-team-grid">
            {teamMembers.map((member, index) => (
              <div key={index} className="about-team-card">
                <div className="about-team-image">
                  <img src={member.image} alt={member.name} />
                  <div className="about-team-overlay">
                    <div className="about-team-social">
                      <a href="#" aria-label="LinkedIn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                      </a>
                      <a href="#" aria-label="Twitter">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
                <div className="about-team-info">
                  <h3 className="about-team-name">{member.name}</h3>
                  <p className="about-team-position">{member.position}</p>
                  <p className="about-team-bio">{member.bio}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default AboutUs;
